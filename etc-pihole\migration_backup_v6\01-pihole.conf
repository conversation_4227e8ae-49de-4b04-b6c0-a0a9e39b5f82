# Pi-hole: A black hole for Internet advertisements
# (c) 2017 Pi-hole, LLC (https://pi-hole.net)
# Network-wide ad blocking via your own hardware.
#
# Dnsmasq config for Pi-hole's FTLDNS
#
# This file is copyright under the latest version of the EUPL.
# Please see LICENSE file for your rights under this license.

###############################################################################
#      FILE AUTOMATICALLY POPULATED BY PI-HOLE INSTALL/UPDATE PROCEDURE.      #
# ANY CHANGES MADE TO THIS FILE AFTER INSTALL WILL BE LOST ON THE NEXT UPDATE #
#                                                                             #
#        IF YOU WISH TO CHANGE THE UPSTREAM SERVERS, CHANGE THEM IN:          #
#                      /etc/pihole/setupVars.conf                             #
#                                                                             #
#        ANY OTHER CHANGES SHOULD BE MADE IN A SEPARATE CONFIG FILE           #
#                    WITHIN /etc/dnsmasq.d/yourname.conf                      #
###############################################################################

addn-hosts=/etc/pihole/local.list
addn-hosts=/etc/pihole/custom.list


localise-queries


no-resolv

log-queries
log-facility=/var/log/pihole/pihole.log

log-async
cache-size=10000
server=*******
server=*******
server=**************
server=**************
server=*******
server=*******
server=**********
server=***********
server=************
server=************
server=*******
server=***************
server=********
server=**************
server=********
server=**************
server=*******
server=*******
local-service
