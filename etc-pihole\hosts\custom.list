# Pi-hole: A black hole for Internet advertisements
# (c) 2025 Pi-hole, LLC (https://pi-hole.net)
# Network-wide ad blocking via your own hardware.
#
# Custom DNS entries (HOSTS file)
#
##################################################################################
#                                                                                #
#                     FILE AUTOMATICALLY POPULATED BY PI-HOLE                    #
#    ANY CHANGES MADE TO THIS FILE WILL BE LOST WHEN THE CONFIGURATION CHANGES   #
#                                                                                #
#            IF YOU WISH TO CHANGE ANY OF THESE VALUES, CHANGE THEM IN           #
#                             /etc/pihole/pihole.toml                            #
#                             and restart pihole-FTL                             #
#                                                                                #
#           ANY OTHER CHANGES SHOULD BE MADE IN A SEPARATE CONFIG FILE           #
#                       WITHIN /etc/dnsmasq.d/yourname.conf                      #
#    (make sure misc.etc_dnsmasq_d is set to true in /etc/pihole/pihole.toml)    #
#                                                                                #
#                      Last updated: 2025-07-03 20:09:22 UTC                     #
#                              by FTL version v6.2.3                             #
#                                                                                #
##################################################################################

192.168.1.111 proxy.local
192.168.1.111 pihole.local
192.168.1.111 owui.local
192.168.1.111 mysearch.local


# There are 4 entries in this file
