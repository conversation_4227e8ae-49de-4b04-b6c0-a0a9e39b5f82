# Pi-hole: A black hole for Internet advertisements
# (c) 2021 Pi-hole, LLC (https://pi-hole.net)
# Network-wide ad blocking via your own hardware.
#
# RFC 6761 config file for Pi-hole
#
# This file is copyright under the latest version of the EUPL.
# Please see LICENSE file for your rights under this license.

###############################################################################
#      FILE AUTOMATICALLY POPULATED BY PI-HOLE INSTALL/UPDATE PROCEDURE.      #
# ANY CHANGES MADE TO THIS FILE AFTER INSTALL WILL BE LOST ON THE NEXT UPDATE #
#                                                                             #
#             CHANGES SHOULD BE MADE IN A SEPARATE CONFIG FILE                #
#                    WITHIN /etc/dnsmasq.d/yourname.conf                      #
###############################################################################

# RFC 6761: Caching DNS servers SHOULD recognize
#     test, localhost, invalid
# names as special and SHOULD NOT attempt to look up NS records for them, or
# otherwise query authoritative DNS servers in an attempt to resolve these
# names.
server=/test/
server=/localhost/
server=/invalid/

# The same RFC requests something similar for
#     10.in-addr.arpa.      21.172.in-addr.arpa.  27.172.in-addr.arpa.
#     16.172.in-addr.arpa.  22.172.in-addr.arpa.  28.172.in-addr.arpa.
#     17.172.in-addr.arpa.  23.172.in-addr.arpa.  29.172.in-addr.arpa.
#     18.172.in-addr.arpa.  24.172.in-addr.arpa.  30.172.in-addr.arpa.
#     19.172.in-addr.arpa.  25.172.in-addr.arpa.  31.172.in-addr.arpa.
#     20.172.in-addr.arpa.  26.172.in-addr.arpa.  168.192.in-addr.arpa.
# Pi-hole implements this via the dnsmasq option "bogus-priv" (see
# 01-pihole.conf) because this also covers IPv6.

# OpenWRT furthermore blocks    bind, local, onion    domains
# see https://git.openwrt.org/?p=openwrt/openwrt.git;a=blob_plain;f=package/network/services/dnsmasq/files/rfc6761.conf;hb=HEAD
# and https://www.iana.org/assignments/special-use-domain-names/special-use-domain-names.xhtml
# We do not include the ".local" rule ourselves, see https://github.com/pi-hole/pi-hole/pull/4282#discussion_r689112972
server=/bind/
server=/onion/
