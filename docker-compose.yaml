version: "3"

services:
  pihole:
    image: pihole/pihole:latest
    container_name: pihole
    ports:
      - "53:53/tcp" # DNS port for TCP
      - "53:53/udp" # DNS port for UDP
      - "88:80/tcp" # Pi-hole web interface accessible on port 80
      - "67:67/udp" # DHCP port (if you enable DHCP in Pi-hole)
    volumes:
      - "./etc-pihole:/etc/pihole:rw"
      - "./etc-dnsmasq.d:/etc/dnsmasq.d:rw"
    cap_add:
      - NET_ADMIN
    environment:
      FTLCONF_LOCAL_IPV4: localhost # Set to your host IP
      WEBPASSWORD: "12345@qwerty" # Web interface password
    restart: always
    networks:
      - pihole_net

networks:
  pihole_net:
    driver: bridge
