# Pi-hole: A black hole for Internet advertisements
# (c) 2025 Pi-hole, LLC (https://pi-hole.net)
# Network-wide ad blocking via your own hardware.
#
# Dnsmasq config for Pi-hole's FTLDNS
#
##################################################################################
#                                                                                #
#                     FILE AUTOMATICALLY POPULATED BY PI-HOLE                    #
#    ANY CHANGES MADE TO THIS FILE WILL BE LOST WHEN THE CONFIGURATION CHANGES   #
#                                                                                #
#            IF YOU WISH TO CHANGE ANY OF THESE VALUES, CHANGE THEM IN           #
#                             /etc/pihole/pihole.toml                            #
#                             and restart pihole-FTL                             #
#                                                                                #
#           ANY OTHER CHANGES SHOULD BE MADE IN A SEPARATE CONFIG FILE           #
#                       WITHIN /etc/dnsmasq.d/yourname.conf                      #
#    (make sure misc.etc_dnsmasq_d is set to true in /etc/pihole/pihole.toml)    #
#                                                                                #
#                      Last updated: 2025-07-03 20:09:22 UTC                     #
#                              by FTL version v6.2.3                             #
#                                                                                #
##################################################################################
hostsdir=/etc/pihole/hosts

# Don't read /etc/resolv.conf. Get upstream servers only from the configuration
no-resolv

# DNS port to be used
port=53

# List of upstream DNS server
server=*******
server=*******
server=**************
server=**************
server=*******
server=*******
server=**********
server=***********
server=************
server=************
server=*******
server=***************
server=********
server=**************
server=********
server=**************
server=*******
server=*******

# Set the size of dnsmasq's cache. The default is 150 names. Setting the cache
# size to zero disables caching. Note: huge cache size impacts performance
cache-size=10000

# Return answers to DNS queries from /etc/hosts and interface-name and
# dynamic-host which depend on the interface over which the query was
# received. If a name has more than one address associated with it, and
# at least one of those addresses is on the same subnet as the interface
# to which the query was sent, then return only the address(es) on that
# subnet and return all the available addresses otherwise.
localise-queries

# Enable query logging
log-queries
log-async

# Specify the log file to use
# We set this even if logging is disabled to store warnings
# and errors in this file. This is useful for debugging.
log-facility=/var/log/pihole/pihole.log

# Use stale cache entries for a given number of seconds to optimize cache utilization
# Setting the time to zero will serve stale cache data regardless how long it has expired.
use-stale-cache=3600

# Only respond to queries from devices that are at most one hop away (local devices)
local-service

# Do not forward home.arpa domains to upstream servers
local=/home.arpa/

# DNS domain for both the DNS and DHCP server
# This DNS domain is purely local. FTL may answer queries from
# /etc/hosts or DHCP but should never forward queries on that
# domain to any upstream servers
domain=lan
local=/lan/

# Local domain for Pi-hole
# This domain is purely local and should never be forwarded to any
# upstream servers. We add a false A-record to this domain to prevent
# NXDOMAIN responses for queries on this domain. The actual response
# is handled by FTL at runtime
local=/pi.hole/
host-record=pi.hole,0.0.0.0
# User-defined custom CNAMEs
cname=searxng.local,*************

# RFC 6761: Caching DNS servers SHOULD recognize
#     test, localhost, invalid
# names as special and SHOULD NOT attempt to look up NS records for them, or
# otherwise query authoritative DNS servers in an attempt to resolve these
# names.
server=/test/
server=/localhost/
server=/invalid/

# The same RFC requests something similar for
#     10.in-addr.arpa.      21.172.in-addr.arpa.  27.172.in-addr.arpa.
#     16.172.in-addr.arpa.  22.172.in-addr.arpa.  28.172.in-addr.arpa.
#     17.172.in-addr.arpa.  23.172.in-addr.arpa.  29.172.in-addr.arpa.
#     18.172.in-addr.arpa.  24.172.in-addr.arpa.  30.172.in-addr.arpa.
#     19.172.in-addr.arpa.  25.172.in-addr.arpa.  31.172.in-addr.arpa.
#     20.172.in-addr.arpa.  26.172.in-addr.arpa.  168.192.in-addr.arpa.
# Pi-hole implements this via the dnsmasq option "bogus-priv" above
# (if enabled!) as this option also covers IPv6.

# OpenWRT furthermore blocks bind, local, onion domains
# see https://git.openwrt.org/?p=openwrt/openwrt.git;a=blob_plain;f=package/network/services/dnsmasq/files/rfc6761.conf;hb=HEAD
# and https://www.iana.org/assignments/special-use-domain-names/special-use-domain-names.xhtml
# We do not include the ".local" rule ourselves, see https://github.com/pi-hole/pi-hole/pull/4282#discussion_r689112972
server=/bind/
server=/onion/

# Cache all DNS records
cache-rr=ANY

# RFC 8482: Providing Minimal-Sized Responses to DNS Queries That Have QTYPE=ANY
# Filters replies to queries for type ANY. Everything other than A, AAAA, MX and CNAME
# records are removed. Since ANY queries with forged source addresses can be used in DNS amplification attacks
# replies to ANY queries can be large) this defangs such attacks, whilst still supporting the
# one remaining possible use of ANY queries. See RFC 8482 para 4.3 for details.
filter-rr=ANY

